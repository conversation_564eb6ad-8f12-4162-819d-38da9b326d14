/**
 * Markdown processing utilities
 */

'use client';

import { marked } from 'marked';
import type { Renderer } from 'marked';
import DOMPurify from 'dompurify';

// Define custom renderer methods
const rendererMethods = {
  heading(text: string | undefined, level: number) {
    // Convert any input to string and handle undefined
    const rawText = String(text || '');
    
    // Create ID for heading
    const id = rawText
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/^-+|-+$/g, '')
      .replace(/-+/g, '-')
      || 'heading';

    return `<h${level} id="${id}">${rawText}</h${level}>`;
  },

  code(code: string | undefined, language: string | undefined) {
    // Get the code string, default to empty string if undefined
    const codeStr = typeof code === 'string' ? code : String(code || '');
    
    const langStr = typeof language === 'string' ? language : String(language || '');
    const validLanguage = langStr && langStr.match(/^[a-zA-Z0-9_+-]*$/);
    const langClass = validLanguage ? `language-${langStr}` : '';

    // Escape HTML entities in code content
    const escapedCode = codeStr
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');

    return `
      <div class="code-block-wrapper">
        <div class="code-block-header">
          <span class="code-language">${langStr || 'text'}</span>
          <button class="copy-code-btn" data-code="${encodeURIComponent(codeStr)}">
            Copy
          </button>
        </div>
        <pre class="language-${langStr || 'text'}"><code class="hljs ${langClass}">${escapedCode.trimEnd()}</code></pre>
      </div>
    `;
  },

  // Add proper text content handling
  text(text: string | undefined) {
    // Always convert to string and return
    return String(text || '');
  },

  table(header: string, body: string) {
    const headerStr = typeof header === 'string' ? header : String(header || '');
    const bodyStr = typeof body === 'string' ? body : String(body || '');

    return `
      <div class="table-wrapper">
        <table class="markdown-table">
          <thead>${headerStr}</thead>
          <tbody>${bodyStr}</tbody>
        </table>
      </div>
    `;
  },

  link(href: string | null, title: string | null, text: string) {
    const hrefStr = typeof href === 'string' ? href : String(href || '');
    const textStr = typeof text === 'string' ? text : String(text || '');
    const titleStr = title ? String(title) : null;

    const isExternal = hrefStr.startsWith('http') || hrefStr.startsWith('//');
    const target = isExternal ? 'target="_blank" rel="noopener noreferrer"' : '';
    const titleAttr = titleStr ? `title="${titleStr}"` : '';

    const escapedText = textStr
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');

    return `<a href="${hrefStr}" ${target} ${titleAttr}>${escapedText}</a>`;
  },

  paragraph(text: string) {
    // Convert to string and wrap in paragraph
    return `<p>${String(text || '')}</p>`;
  },

  strong(text: string) {
    // Convert to string and wrap in strong
    return `<strong>${String(text || '')}</strong>`;
  },

  em(text: string) {
    // Convert to string and wrap in em
    return `<em>${String(text || '')}</em>`;
  },

  codespan(code: string) {
    if (!code) return '';
    const escapedCode = code
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
    return `<code class="inline-code">${escapedCode}</code>`;
  }
};

// Initialize marked with custom rendering
const renderer = new marked.Renderer();

// Override renderer methods
Object.entries(rendererMethods).forEach(([method, handler]) => {
  (renderer as any)[method] = handler;
});

// Configure marked
marked.setOptions({
  renderer,
  gfm: true,
  breaks: true,
  pedantic: false
});

// Process the markdown with custom handling
function processMarkdown(markdown: string): string {
  // Process code blocks separately
  const codeBlocks = new Map<string, string>();
  let processedMarkdown = markdown.replace(/```([\s\S]*?)```/g, (match, content) => {
    const id = Math.random().toString(36).slice(2);
    codeBlocks.set(id, match);
    return `CODE_BLOCK_${id}`;
  });

  // Parse with marked
  let processedHtml = marked.parse(processedMarkdown, {
    async: false
  }) as string;

  // Replace code blocks with properly formatted versions
  for (const [id, block] of codeBlocks.entries()) {
    const match = block.match(/```(?:(\w+)\n)?([\s\S]*?)```/);
    if (match) {
      const [, lang, code] = match;
      const language = lang || 'text';
      const escapedCode = code.trim()
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');

      const codeHtml = `
        <div class="code-block-wrapper">
          <div class="code-block-header">
            <span class="code-language">${language}</span>
            <button class="copy-code-btn" data-code="${encodeURIComponent(code.trim())}">Copy</button>
          </div>
          <pre class="language-${language}"><code class="hljs ${language ? `language-${language}` : ''}">${escapedCode}</code></pre>
        </div>
      `;

      processedHtml = processedHtml
        .replace(`<p>CODE_BLOCK_${id}</p>`, codeHtml)
        .replace(`CODE_BLOCK_${id}`, codeHtml);
    }
  }

  return processedHtml;
}


// Add delete confirmation handler
export function confirmDelete(message: string = 'Are you sure you want to delete this?'): boolean {
  return window.confirm(message);
}

/**
 * Convert markdown to sanitized HTML
 */
export function markdownToHtml(markdown: string): string {
  try {
    // Ensure input is a string and process it
    const markdownStr = String(markdown || '');
    const html = processMarkdown(markdownStr);

    // Sanitize the HTML with strict string handling
    const cleanHtml = DOMPurify.sanitize(String(html), {
        ALLOWED_TAGS: [
          // Headers and text formatting
          'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
          'p', 'br', 'strong', 'em', 'b', 'i',
          // Code elements
          'code', 'pre',
          // Layout and containers
          'div', 'span',
          // Interactive elements
          'button', 'a',
          // Lists
          'ul', 'ol', 'li',
          // Block elements
          'blockquote',
          // Tables
          'table', 'thead', 'tbody', 'tr', 'th', 'td'
        ],
        ALLOWED_ATTR: [
          // Common attributes
          'class', 'id', 'style',
          // Accessibility
          'aria-label', 'role',
          // Links
          'href', 'title', 'rel', 'target',
          // Code blocks
          'data-code', 'language',
          // Copy button
          'data-clipboard-text'
        ],
        ADD_TAGS: ['pre', 'code'],
        ADD_ATTR: ['class', 'data-code', 'data-clipboard-text'],
        ALLOW_DATA_ATTR: true,
        USE_PROFILES: { html: true },
        RETURN_DOM_FRAGMENT: false,
        RETURN_DOM: false,
        WHOLE_DOCUMENT: false
    });

    // Ensure final output is a string
    return String(cleanHtml);
  } catch (error) {
    console.error('Error converting markdown to HTML:', error);
    return '<p>Error rendering markdown content</p>';
  }
}

/**
 * Extract headings from markdown for table of contents
 */
export function extractHeadings(markdown: string): Array<{
  level: number;
  text: string;
  id: string;
}> {
  const headingRegex = /^(#{1,6})\s+(.+)$/gm;
  const headings: Array<{ level: number; text: string; id: string }> = [];

  let match;
  while ((match = headingRegex.exec(markdown)) !== null) {
    const level = match[1].length;
    const text = match[2].trim();
    const id = text.toLowerCase().replace(/[^\w]+/g, '-');

    headings.push({ level, text, id });
  }

  return headings;
}

/**
 * Get word count from markdown
 */
export function getWordCount(markdown: string): {
  words: number;
  characters: number;
  charactersNoSpaces: number;
  paragraphs: number;
  readingTime: number; // in minutes
} {
  // Remove markdown syntax for accurate word count
  const plainText = markdown
    .replace(/#{1,6}\s+/g, '') // Remove headers
    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
    .replace(/\*(.*?)\*/g, '$1') // Remove italic
    .replace(/`(.*?)`/g, '$1') // Remove inline code
    .replace(/```[\s\S]*?```/g, '') // Remove code blocks
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Remove links, keep text
    .replace(/!\[([^\]]*)\]\([^)]+\)/g, '') // Remove images
    .replace(/^\s*[-*+]\s+/gm, '') // Remove list markers
    .replace(/^\s*\d+\.\s+/gm, '') // Remove numbered list markers
    .replace(/^\s*>\s+/gm, '') // Remove blockquotes
    .replace(/^\s*\|.*\|$/gm, '') // Remove tables
    .replace(/^\s*[-=]{3,}$/gm, '') // Remove horizontal rules
    .trim();

  const words = plainText.split(/\s+/).filter(word => word.length > 0).length;
  const characters = plainText.length;
  const charactersNoSpaces = plainText.replace(/\s/g, '').length;
  const paragraphs = plainText.split(/\n\s*\n/).filter(p => p.trim().length > 0).length;

  // Average reading speed: 200 words per minute
  const readingTime = Math.ceil(words / 200);

  return {
    words,
    characters,
    charactersNoSpaces,
    paragraphs,
    readingTime
  };
}

/**
 * Validate markdown syntax
 */
export function validateMarkdown(markdown: string): {
  isValid: boolean;
  errors: Array<{
    line: number;
    message: string;
    type: 'warning' | 'error';
  }>;
} {
  const errors: Array<{ line: number; message: string; type: 'warning' | 'error' }> = [];
  const lines = markdown.split('\n');

  lines.forEach((line, index) => {
    const lineNumber = index + 1;

    // Check for unmatched brackets
    const openBrackets = (line.match(/\[/g) || []).length;
    const closeBrackets = (line.match(/\]/g) || []).length;
    if (openBrackets !== closeBrackets) {
      errors.push({
        line: lineNumber,
        message: 'Unmatched square brackets',
        type: 'warning'
      });
    }

    // Check for unmatched parentheses in links
    const openParens = (line.match(/\(/g) || []).length;
    const closeParens = (line.match(/\)/g) || []).length;
    if (openParens !== closeParens) {
      errors.push({
        line: lineNumber,
        message: 'Unmatched parentheses',
        type: 'warning'
      });
    }

    // Check for malformed links
    const linkRegex = /\[([^\]]*)\]\(([^)]*)\)/g;
    let linkMatch;
    while ((linkMatch = linkRegex.exec(line)) !== null) {
      const linkText = linkMatch[1];
      const linkUrl = linkMatch[2];

      if (!linkText.trim()) {
        errors.push({
          line: lineNumber,
          message: 'Link has empty text',
          type: 'warning'
        });
      }

      if (!linkUrl.trim()) {
        errors.push({
          line: lineNumber,
          message: 'Link has empty URL',
          type: 'error'
        });
      }
    }

    // Check for malformed images
    const imageRegex = /!\[([^\]]*)\]\(([^)]*)\)/g;
    let imageMatch;
    while ((imageMatch = imageRegex.exec(line)) !== null) {
      const altText = imageMatch[1];
      const imageUrl = imageMatch[2];

      if (!imageUrl.trim()) {
        errors.push({
          line: lineNumber,
          message: 'Image has empty URL',
          type: 'error'
        });
      }
    }
  });

  return {
    isValid: errors.filter(e => e.type === 'error').length === 0,
    errors
  };
}

/**
 * Format markdown content
 */
export function formatMarkdown(markdown: string): string {
  return markdown
    .replace(/\n{3,}/g, '\n\n') // Remove excessive line breaks
    .replace(/[ \t]+$/gm, '') // Remove trailing whitespace
    .replace(/^[ \t]+/gm, (match) => match.replace(/\t/g, '  ')) // Convert tabs to spaces
    .trim();
}

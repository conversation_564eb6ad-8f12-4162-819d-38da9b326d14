/**
 * Export utilities for different file formats
 */

import { ExportError } from '@/types';
import { markdownToHtml } from './markdown';
import { downloadFile } from './file';

interface ExportOptions {
  format: 'md' | 'txt' | 'html' | 'pdf' | 'docx';
  pageSize?: string;
  margins?: { top: number; right: number; bottom: number; left: number };
  includeMetadata?: boolean;
}

/**
 * Export markdown as plain text
 */
export async function exportAsText(
  markdown: string,
  filename: string
): Promise<void> {
  try {
    const textFilename = filename.replace(/\.[^/.]+$/, '.txt');
    downloadFile(markdown, textFilename, 'text/plain');
  } catch (error) {
    throw new ExportError('Failed to export as text', 'txt', error);
  }
}

/**
 * Export markdown as HTML
 */
export async function exportAsHtml(
  markdown: string,
  filename: string,
  options: Partial<ExportOptions> = {}
): Promise<void> {
  try {
    const htmlContent = markdownToHtml(markdown);

    const fullHtml = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${filename}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.75;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            color: #374151;
            background: #ffffff;
        }

        h1, h2, h3, h4, h5, h6 {
            margin-top: 2rem;
            margin-bottom: 1rem;
            font-weight: 600;
            color: #111827;
        }

        h1 { font-size: 2.25rem; line-height: 2.5rem; }
        h2 { font-size: 1.875rem; line-height: 2.25rem; }
        h3 { font-size: 1.5rem; line-height: 2rem; }
        h4 { font-size: 1.25rem; line-height: 1.75rem; }
        h5 { font-size: 1.125rem; line-height: 1.75rem; }
        h6 { font-size: 1rem; line-height: 1.5rem; }

        p {
            margin-bottom: 1rem;
            line-height: 1.75;
        }

        /* Code styling to match preview */
        .code-block-wrapper {
            margin: 1rem 0;
            border-radius: 0.5rem;
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }

        .code-block-header {
            background-color: #f9fafb;
            padding: 0.5rem 1rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .code-language {
            font-size: 0.75rem;
            font-weight: 500;
            color: #6b7280;
            text-transform: uppercase;
        }

        .copy-code-btn {
            display: none; /* Hide in exports */
        }

        .code-block-wrapper pre {
            background-color: #f3f4f6;
            padding: 1rem;
            margin: 0;
            overflow-x: auto;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 0.875rem;
            line-height: 1.6;
        }

        .code-block-wrapper code {
            background: none;
            padding: 0;
            color: #374151;
        }

        /* Inline code */
        code:not(.code-block-wrapper code) {
            background-color: #f3f4f6;
            padding: 0.125rem 0.25rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            color: #374151;
        }

        /* Table styling to match preview */
        .table-wrapper {
            overflow-x: auto;
            margin: 1rem 0;
        }

        .markdown-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .markdown-table th,
        .markdown-table td {
            border: 1px solid #e5e7eb;
            padding: 0.5rem;
            text-align: left;
        }

        .markdown-table th {
            background-color: #f9fafb;
            font-weight: 600;
            color: #374151;
        }

        blockquote {
            border-left: 4px solid #e5e7eb;
            margin: 1rem 0;
            padding-left: 1rem;
            color: #6b7280;
            font-style: italic;
        }

        img {
            max-width: 100%;
            height: auto;
            border-radius: 0.5rem;
        }

        a {
            color: #3b82f6;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }

        ul, ol {
            margin: 1rem 0;
            padding-left: 2rem;
        }

        li {
            margin-bottom: 0.5rem;
        }

        hr {
            border: none;
            border-top: 1px solid #e5e7eb;
            margin: 2rem 0;
        }

        .anchor-link {
            opacity: 0;
            margin-left: 0.5rem;
            text-decoration: none;
            color: #6b7280;
        }

        h1:hover .anchor-link,
        h2:hover .anchor-link,
        h3:hover .anchor-link,
        h4:hover .anchor-link,
        h5:hover .anchor-link,
        h6:hover .anchor-link {
            opacity: 1;
        }

        @media print {
            body {
                margin: 0;
                padding: 1rem;
            }

            .anchor-link {
                display: none;
            }

            .copy-code-btn {
                display: none;
            }
        }
    </style>
</head>
<body>
    ${htmlContent}
</body>
</html>`;

    const htmlFilename = filename.replace(/\.[^/.]+$/, '.html');
    downloadFile(fullHtml, htmlFilename, 'text/html');
  } catch (error) {
    throw new ExportError('Failed to export as HTML', 'html', error);
  }
}

/**
 * Export markdown as PDF (lazy loaded)
 */
export async function exportAsPdf(
  markdown: string,
  filename: string,
  options: Partial<ExportOptions> = {}
): Promise<void> {
  try {
    // Lazy load jsPDF and html2canvas
    const { jsPDF } = await import('jspdf');
    const html2canvas = (await import('html2canvas')).default;
    
    const htmlContent = markdownToHtml(markdown);
    
    // Create a styled container for the HTML content
    const container = document.createElement('div');
    container.innerHTML = htmlContent;
    container.className = 'markdown-export';
    
    // Add export styling
    const exportStyle = document.createElement('style');
    exportStyle.textContent = `
      .markdown-export {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        line-height: 1.75;
        color: #374151;
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
      }
      .markdown-export img {
        max-width: 100%;
        height: auto;
      }
      .markdown-export pre {
        background: #f3f4f6;
        padding: 1rem;
        border-radius: 4px;
        overflow-x: auto;
      }
      .markdown-export table {
        width: 100%;
        border-collapse: collapse;
        margin: 1rem 0;
      }
      .markdown-export th, .markdown-export td {
        border: 1px solid #e5e7eb;
        padding: 0.5rem;
      }
      .markdown-export ul, .markdown-export ol {
        padding-left: 2rem;
      }
    `;
    document.head.appendChild(exportStyle);
    document.body.appendChild(container);

    try {
      // Convert the container to canvas
      const canvas = await html2canvas(container, {
        scale: 2,
        useCORS: true,
        logging: false
      });

      // Initialize PDF with proper dimensions
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: options.pageSize || 'a4'
      });

      // Add the canvas as an image
      const imgData = canvas.toDataURL('image/jpeg', 1.0);
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      pdf.addImage(imgData, 'JPEG', 0, 0, pageWidth, pageHeight);

      // Save the PDF
      const pdfFilename = filename.replace(/\.[^/.]+$/, '.pdf');
      pdf.save(pdfFilename);
  } catch (error) {
    throw new ExportError('Failed to export as PDF', 'pdf', error);
  }
}

/**
 * Export markdown as DOCX (lazy loaded)
 */
/**
 * Export markdown as DOCX
 */
/**
 * Export markdown as DOCX
 */
export async function exportAsDocx(
  markdown: string,
  filename: string,
  options: Partial<ExportOptions> = {}
): Promise<void> {
  try {
    // Dynamic imports
    const docx = await import('docx');
    const marked = await import('marked');
    
    // Extract needed types and functions
    const { Document, Packer, Paragraph, TextRun, HeadingLevel, TableCell, TableRow, Table, AlignmentType } = docx;
    const { marked: markdownParser } = marked;

    const tokens = markdownParser.lexer(markdown);
    const paragraphs: any[] = [];

    // Helper function to handle inline formatting
    const processInlineText = (text: string) => {
      const runs: any[] = [];
      const inlineTokens = markdownParser.lexer(text);
      
      for (const token of inlineTokens) {
        if (token.type === 'strong') {
          runs.push(new TextRun({ text: token.text, bold: true }));
        } else if (token.type === 'em') {
          runs.push(new TextRun({ text: token.text, italics: true }));
        } else if (token.type === 'codespan') {
          runs.push(new TextRun({
            text: token.text,
            font: 'Consolas',
            size: 20
          }));
        } else if (token.type === 'text') {
          runs.push(new TextRun({ text: token.raw }));
        }
      }

      return runs;
    };

    // Process tokens
    for (const token of tokens) {
      switch (token.type) {
        case 'heading':
          paragraphs.push(new Paragraph({
            text: token.text,
            heading: HeadingLevel[`HEADING_${Math.min(token.depth, 6)}`],
            spacing: { before: 200, after: 100 }
          }));
          break;

        case 'paragraph':
          paragraphs.push(new Paragraph({
            children: processInlineText(token.text),
            spacing: { after: 100 }
          }));
          break;

        case 'list':
          for (const item of token.items) {
            paragraphs.push(new Paragraph({
              children: processInlineText(item.text),
              bullet: { level: 0 },
              spacing: { after: 80 }
            }));
          }
          break;

        case 'code':
          paragraphs.push(new Paragraph({
            children: [new TextRun({
              text: token.text,
              font: 'Consolas',
              size: 20
            })],
            spacing: { before: 120, after: 120 },
            shading: { type: 'solid', color: 'F3F4F6' }
          }));
          break;

        case 'table':
          const table = new Table({
            rows: [
              new TableRow({
                children: token.header.map((headerText: string) =>
                  new TableCell({
                    children: [new Paragraph({
                      children: processInlineText(headerText),
                      alignment: AlignmentType.LEFT
                    })],
                    shading: { type: 'solid', color: 'F9FAFB' }
                  })
                )
              }),
              ...token.rows.map((row: string[]) =>
                new TableRow({
                  children: row.map((cellText: string) =>
                    new TableCell({
                      children: [new Paragraph({
                        children: processInlineText(cellText)
                      })]
                    })
                  )
                })
              )
            ],
            width: { size: 100, type: 'pct' }
          });
          paragraphs.push(table);
          break;

        case 'blockquote':
          paragraphs.push(new Paragraph({
            children: processInlineText(token.text),
            spacing: { before: 120, after: 120 },
            indent: { left: 400 },
            shading: { type: 'solid', color: 'F9FAFB' }
          }));
          break;

        case 'hr':
          paragraphs.push(new Paragraph({
            text: '―'.repeat(30),
            alignment: AlignmentType.CENTER,
            spacing: { before: 200, after: 200 }
          }));
          break;
      }
    }

    // Create document
    const doc = new Document({
      sections: [{
        properties: {},
        children: paragraphs
      }]
    });

    // Generate buffer and create download
    const buffer = await Packer.toBuffer(doc);
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    });

    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename.replace(/\.[^/.]+$/, '.docx');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

  } catch (error) {
    throw new ExportError('Failed to export as DOCX', 'docx', error);
  }
}

    const doc = new Document({
      sections: [{
        properties: {},
        children: paragraphs
      }]
    });

    // Generate and download the document
    // Create document
    const docToSave = new Document({
      sections: [{
        properties: {},
        children: paragraphs
      }]
    });

    // Generate buffer
    const buffer = await Packer.toBuffer(docToSave);
    
    // Create blob and download
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    });

    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename.replace(/\.[^/.]+$/, '.docx');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

  } catch (error) {
    throw new ExportError('Failed to export as DOCX', 'docx', error);
  }
}
    // Generate and download the document
    const buffer = await Packer.toBuffer(doc);
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    });

    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename.replace(/\.[^/.]+$/, '.docx');
    link.style.display = 'none';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    setTimeout(() => URL.revokeObjectURL(url), 100);
  } catch (error) {
    throw new ExportError('Failed to export as DOCX', 'docx', error);
  }
}

/**
 * Main export function that delegates to specific format handlers
 */
export async function exportAsMarkdown(
  markdown: string,
  filename: string
): Promise<void> {
  try {
    const mdFilename = filename.replace(/\.[^/.]+$/, '.md');
    downloadFile(markdown, mdFilename, 'text/markdown');
  } catch (error) {
    throw new ExportError('Failed to export as Markdown', 'md', error);
  }
}

export async function exportMarkdown(
  markdown: string,
  filename: string,
  options: ExportOptions
): Promise<void> {
  switch (options.format) {
    case 'md':
      return exportAsMarkdown(markdown, filename);
    case 'txt':
      return exportAsText(markdown, filename);
    case 'html':
      return exportAsHtml(markdown, filename, options);
    case 'pdf':
      return exportAsPdf(markdown, filename, options);
    case 'docx':
      return exportAsDocx(markdown, filename, options);
    default:
      throw new ExportError(`Unsupported export format: ${options.format}`, options.format);
  }
}

/**
 * Get available export formats
 */
export function getAvailableFormats(): Array<{
  value: string;
  label: string;
  description: string;
}> {
  return [
    {
      value: 'txt',
      label: 'Plain Text',
      description: 'Export as plain text file (.txt)'
    },
    {
      value: 'html',
      label: 'HTML',
      description: 'Export as HTML file (.html)'
    },
    {
      value: 'pdf',
      label: 'PDF',
      description: 'Export as PDF document (.pdf)'
    },
    {
      value: 'docx',
      label: 'Word Document',
      description: 'Export as Microsoft Word document (.docx)'
    }
  ];
}
